@echo off
set VENV_DIR=.\venv

REM Check if venv directory exists and delete it
if exist "%VENV_DIR%" (
    echo Deleting existing virtual environment folder...
    rmdir /s /q "%VENV_DIR%"
    if %errorlevel% neq 0 (
        echo Failed to delete existing virtual environment folder.
        pause
        exit /b %errorlevel%
    )
    echo Existing virtual environment folder deleted.
)

REM Check if venv directory exists
if not exist "%VENV_DIR%\Scripts\activate.bat" (
    echo Creating Python virtual environment 'venv'...
    py -3.12 -m venv venv
    if %errorlevel% neq 0 (
        echo Failed to create virtual environment. Please ensure Python 3 is installed and in your system PATH.
        pause
        exit /b %errorlevel%
    )
    echo Virtual environment created.
) else (
    echo Virtual environment 'venv' already exists.
)

REM Activate venv and install requirements
echo Activating virtual environment and installing packages...
call "%VENV_DIR%\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo Failed to activate virtual environment.
    pause
    exit /b %errorlevel%
)

echo Upgrading pip...
python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo Failed to upgrade pip.
    pause
    exit /b %errorlevel%
)

echo Installing packages from requirements.txt...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo Failed to install packages from requirements.txt. Check requirements.txt and network connection.
    pause
    exit /b %errorlevel%
)

echo Python environment setup complete.
pause
exit /b 0
