import aiohttp
import asyncio
import base64
import io
import atexit
from PIL import Image
import time
import warnings
import os
import re
from typing import List, Optional, Tuple
from dotenv import load_dotenv

load_dotenv()

# Default values are provided as a fallback, though they should be set in .env
IMAGE_WIDTH_HEIGHT = int(os.getenv("IMAGE_WIDTH_HEIGHT", 500))

# Maximum number of parallel image processing requests
MAX_PARALLEL_IMAGE_REQUESTS = int(os.getenv("MAX_PARALLEL_IMAGE_REQUESTS", 15))

# Suppress InsecureRequestWarning
warnings.filterwarnings("ignore", message="Unverified HTTPS request")

# Global HTTP session for reuse
_GLOBAL_SESSION = None

# Global semaphore to limit parallel image processing
_IMAGE_PROCESSING_SEMAPHORE = None

def get_image_processing_semaphore():
    """Get or create the global semaphore for limiting parallel image processing."""
    global _IMAGE_PROCESSING_SEMAPHORE
    if _IMAGE_PROCESSING_SEMAPHORE is None:
        _IMAGE_PROCESSING_SEMAPHORE = asyncio.Semaphore(MAX_PARALLEL_IMAGE_REQUESTS)
    return _IMAGE_PROCESSING_SEMAPHORE

async def get_http_session():
    """Get or create the global HTTP session with proper limits."""
    global _GLOBAL_SESSION
    if _GLOBAL_SESSION is None or _GLOBAL_SESSION.closed:
        connector = aiohttp.TCPConnector(
            ssl=False,
            limit=10,  # Total connection pool size
            limit_per_host=5,  # Max connections per host
            ttl_dns_cache=300,  # DNS cache TTL
            use_dns_cache=True,
        )
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        _GLOBAL_SESSION = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        # Register cleanup on exit
        atexit.register(cleanup_session)
    return _GLOBAL_SESSION

def cleanup_session():
    """Cleanup the global session."""
    global _GLOBAL_SESSION
    if _GLOBAL_SESSION and not _GLOBAL_SESSION.closed:
        # Create a new event loop if none exists
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        if loop.is_running():
            # If loop is running, schedule cleanup
            asyncio.create_task(_GLOBAL_SESSION.close())
        else:
            # If loop is not running, run cleanup
            loop.run_until_complete(_GLOBAL_SESSION.close())


def normalize_ebay_url(url: str) -> str:
    """
    Normalize eBay image URLs to use the optimized format.

    Args:
        url (str): Original eBay image URL

    Returns:
        str: Normalized eBay image URL
    """
    # try to pull ID from the "/z/<ID>/" form
    m = re.search(r'/z/([^/]+)/', url)
    if m:
        img_id = m.group(1)
    else:
        # otherwise, maybe it's already in the "/images/g/<ID>/" form
        m2 = re.search(r'/images/g/([^/]+)/', url)
        if m2:
            img_id = m2.group(1)
        else:
            # if no ID found, leave it unchanged (or skip)
            return url
    return f"https://i.ebayimg.com/images/g/{img_id}/s-l{IMAGE_WIDTH_HEIGHT}.webp"


async def encode_image_array_from_urls_to_base64(
    image_urls: List[str],
    target_dimensions: Tuple[int, int] = (IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT)
) -> List[Optional[str]]:
    """
    Encode multiple images from URLs in parallel using async/await.
    Limited to MAX_PARALLEL_IMAGE_REQUESTS concurrent requests.

    Args:
        image_urls (list): List of image URLs to encode
        target_dimensions (tuple): Target (width, height) for resizing. Default (IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT).

    Returns:
        list: List of base64 encoded images (None for failed images)
    """
    start_time = time.time()  # Start timing the entire process

    # Split the URLs if they're in a single string separated by semicolons
    if isinstance(image_urls, str):
        image_urls = image_urls.split(';')

    # Filter out empty strings and normalize
    image_urls = [normalize_ebay_url(u) for u in image_urls if u]

    if not image_urls:
        return []

    print(f"Processing {len(image_urls)} images with max {MAX_PARALLEL_IMAGE_REQUESTS} parallel requests")

    # Use the global session to avoid creating too many connections
    session = await get_http_session()

    # Create tasks with semaphore-controlled processing
    tasks = [
        _process_image_with_semaphore(session, url, target_dimensions)
        for url in image_urls
    ]

    # Execute all tasks concurrently (but limited by semaphore)
    encoded_images = await asyncio.gather(*tasks, return_exceptions=True)

    # Process results and handle exceptions
    results = []
    for i, result in enumerate(encoded_images):
        url = image_urls[i]
        if isinstance(result, Exception):
            print(f"Error processing image from {url}: {result}")
            results.append(None)
        else:
            results.append(result)

    total_time = time.time() - start_time
    print(f"Total time to encode {len(image_urls)} images: {total_time:.2f} seconds")

    return results


async def _process_image_with_semaphore(
    session: aiohttp.ClientSession,
    image_url: str,
    target_dimensions: Tuple[int, int]
) -> Optional[str]:
    """
    Process a single image with semaphore control to limit parallel requests.

    Args:
        session: HTTP session for making requests
        image_url: URL of the image to process
        target_dimensions: Target dimensions for resizing

    Returns:
        Base64 encoded image or None if failed
    """
    semaphore = get_image_processing_semaphore()

    async with semaphore:
        return await encode_image_from_url_to_base64(session, image_url, target_dimensions)


async def encode_image_from_url_to_base64(
    session: aiohttp.ClientSession,
    image_url: str,
    target_dimensions: Tuple[int, int] = (IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT)
) -> str:
    """
    Encode a single image from a URL using async HTTP request.

    Args:
        session (aiohttp.ClientSession): HTTP session for making requests
        image_url (str): URL of the image to encode
        target_dimensions (tuple): Target (width, height) for resizing. Default (IMAGE_WIDTH_HEIGHT, IMAGE_WIDTH_HEIGHT).

    Returns:
        str: Base64 encoded image
    """
    start_time = time.time()  # Start timing

    print(image_url)

    # Time the download
    download_start = time.time()
    async with session.get(image_url, ssl=False) as response:
        if response.status == 200:
            content = await response.read()
            download_time = time.time() - download_start

            # Time the image processing
            processing_start = time.time()

            # Load the image from the response content
            image = Image.open(io.BytesIO(content))

            # Resize the image to target_dimensions
            resized_image = resize_image(image, target_dimensions[0], target_dimensions[1])

            # Convert the resized image to bytes
            img_byte_arr = io.BytesIO()
            resized_image.save(img_byte_arr, format=image.format or 'JPEG')
            img_byte_arr = img_byte_arr.getvalue()

            # Encode to base64
            base64_image = base64.b64encode(img_byte_arr).decode("utf-8")

            processing_time = time.time() - processing_start
            total_time = time.time() - start_time

            print(f"Image timing - Download: {download_time:.2f}s, Processing: {processing_time:.2f}s, Total: {total_time:.2f}s")

            return base64_image
        else:
            raise Exception(f"Failed to download image. Status code: {response.status}")


def resize_image(image, width, height):
    """
    Resize an image to the specified width and height only if it's larger than the target dimensions.
    Maintains aspect ratio when resizing.

    Args:
        image (PIL.Image): The image to resize
        width (int): Target width
        height (int): Target height

    Returns:
        PIL.Image: Resized image with dimensions not exceeding width x height while maintaining aspect ratio
    """
    # Get the original dimensions
    orig_width, orig_height = image.size

    # If the image is already smaller than the target dimensions in both width and height,
    # return the original image without resizing
    if orig_width <= width and orig_height <= height:
        return image

    # Calculate the scaling factor to maintain aspect ratio
    scale_width = width / orig_width
    scale_height = height / orig_height
    scale = min(scale_width, scale_height)

    # Calculate new dimensions
    new_width = int(orig_width * scale)
    new_height = int(orig_height * scale)

    # Resize the image while maintaining aspect ratio
    return image.resize((new_width, new_height), Image.LANCZOS)
