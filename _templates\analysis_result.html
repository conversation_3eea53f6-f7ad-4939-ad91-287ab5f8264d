<!DOCTYPE html>
<html>
<head>
    <title>Analysis Results</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 20px;">
    <table style="border-collapse: collapse; width: auto; margin: 0; text-align: left;">
        <thead>
            <tr style="background-color: #f2f2f2;">
                <th style="padding: 8px; border: 1px solid black;">Attribute</th>
                <th style="padding: 8px; border: 1px solid black;">Status</th>
            </tr>
        </thead>
        <tbody>
        {% for attr, value in analysis_data.items() %}
            {% if attr.lower() == "visual evaluation" %}
            <tr>
                <td style="padding: 20px; border: none;"></td>
            </tr>
            {% endif %}

            {% if (value is true or (value is string and value.lower() == "yes")) and attr.lower() != "visual evaluation" %}
            <tr style="background-color: #ffcccc;">
                <td style="padding: 8px; border: 1px solid black;">{{ attr }}</td>
                <td style="padding: 8px; border: 1px solid black; color: #cc0000;">{{ value }}</td>
            </tr>
            {% else %}
            <tr>
                <td style="padding: 8px; border: 1px solid black;">{{ attr }}</td>
                <td style="padding: 8px; border: 1px solid black;">{{ value }}</td>
            </tr>
            {% endif %}
        {% endfor %}
        </tbody>
    </table>
    {{ edit_prompt_form_html | safe }}
</body>
</html>