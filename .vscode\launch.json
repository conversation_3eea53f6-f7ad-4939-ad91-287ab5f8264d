{"version": "0.2.0", "configurations": [{"name": "Python Debugger: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}"}, "python": "${workspaceFolder}/venv/Scripts/python.exe"}, {"name": "Python: FastAPI", "type": "debugpy", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["internalSkuScript:app", "--reload", "--host", "127.0.0.1", "--port", "8000"], "jinja": true, "justMyCode": false, "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}"}, "python": "${workspaceFolder}/venv/Scripts/python.exe"}]}