{"python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.linting.mypyEnabled": true, "python.analysis.typeCheckingMode": "off", "python.analysis.diagnosticMode": "workspace", "python.analysis.autoImportCompletions": true, "python.linting.lintOnSave": true, "python.linting.pylintPath": "pylint", "python.linting.mypyPath": "mypy", "python.analysis.diagnosticSeverityOverrides": {"error": "warning", "reportUndefinedVariable": "warning", "reportUnboundVariable": "warning", "reportGeneralTypeIssues": "warning", "reportOptionalMemberAccess": "warning", "reportOptionalSubscript": "warning", "reportPrivateImportUsage": "warning"}, "python-envs.defaultEnvManager": "ms-python.python:system", "python-envs.pythonProjects": []}