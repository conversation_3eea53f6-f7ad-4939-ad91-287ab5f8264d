[mypy]
python_version = 3.10
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = False
disallow_untyped_decorators = False
no_implicit_optional = False
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_optional = False
disallow_untyped_calls = False
disallow_any_unimported = False

# Library-specific ignores
[mypy.requests]
ignore_missing_imports = True

[mypy.numpy]
ignore_missing_imports = True

[mypy.PIL]
ignore_missing_imports = True

[mypy.PIL.*]
ignore_missing_imports = True

[mypy.concurrent]
ignore_missing_imports = True

[mypy.concurrent.futures]
ignore_missing_imports = True

[mypy.google]
ignore_missing_imports = True

[mypy.google.genai]
ignore_missing_imports = True